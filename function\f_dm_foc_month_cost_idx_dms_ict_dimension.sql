-- Name: f_dm_foc_month_cost_idx_dms_ict_dimension; Type: Function; Schema: fin_dm_opt_foi;
-- 专用于ICT产业量纲颗粒度的月度指数计算函数
-- 抽离自f_dm_foc_month_cost_idx_dms，固化参数：F_INDUSTRY_FLAG='I', F_DIMENSION_TYPE='D', F_ITEM_VERSION=NULL

CREATE OR REPLACE FUNCTION fin_dm_opt_foi.f_dm_foc_month_cost_idx_dms_ict_dimension(OUT x_result_status text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$

/***************************************************************************************************************************************************************
创建时间：2024年12月
创建人  ：系统重构
功能描述：ICT产业量纲颗粒度月度分析指数表数据初始化专用函数
背景描述：从原f_dm_foc_month_cost_idx_dms函数中抽离出专门处理ICT产业量纲颗粒度的逻辑
固化参数：F_INDUSTRY_FLAG = 'I' (ICT产业)
         F_DIMENSION_TYPE = 'D' (量纲颗粒度)  
         F_ITEM_VERSION = NULL (自动获取最新版本)
事例    ：SELECT FIN_DM_OPT_FOI.F_DM_FOC_MONTH_COST_IDX_DMS_ICT_DIMENSION();
****************************************************************************************************************************************************************/

DECLARE
  -- ========== 基础控制变量定义 ==========
  V_SP_NAME                              VARCHAR(50) := 'FIN_DM_OPT_FOI.F_DM_FOC_MONTH_COST_IDX_DMS_ICT_DIMENSION'; -- 存储过程名称，用于日志记录
  V_STEP_NUM                             BIGINT := 0; -- 函数执行步骤计数器，用于日志追踪
  V_SQL                                  TEXT;        -- 动态SQL语句变量
  VERSION_ID                             BIGINT := 62111
BEGIN
  -- 初始化返回状态
  X_RESULT_STATUS := '1';
  
  -- 记录函数开始执行日志
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME,
   F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => V_SP_NAME || '开始执行 - ICT产业量纲颗粒度月度指数计算');
   
  -- 获取ICT产业最新版本号（固化逻辑：F_INDUSTRY_FLAG='I', F_ITEM_VERSION=NULL）
  V_STEP_NUM := V_STEP_NUM + 1;

  -- BEGIN
  --   SELECT VERSION_ID
  --     INTO V_VERSION
  --     FROM FIN_DM_OPT_FOI.DM_FOC_VERSION_INFO_T
  --    WHERE DEL_FLAG = 'N'
  --      AND STATUS = 1
  --      AND UPPER(DATA_TYPE) = 'ITEM'
  --    ORDER BY LAST_UPDATE_DATE DESC 
  --    LIMIT 1;
     
  --   -- 记录版本号获取成功日志
  --   PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  --   (F_SP_NAME => V_SP_NAME,
  --    F_STEP_NUM => V_STEP_NUM,
  --    F_CAL_LOG_DESC => '成功获取ICT产业最新版本号：' || V_VERSION || '，基期：' || V_BASE_PERIOD_ID,
  --    F_RESULT_STATUS => X_RESULT_STATUS,
  --    F_ERRBUF => 'SUCCESS');
     
  -- EXCEPTION
  --   WHEN NO_DATA_FOUND THEN
  --     X_RESULT_STATUS := '0';
  --     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  --     (F_SP_NAME => V_SP_NAME,
  --      F_STEP_NUM => V_STEP_NUM,
  --      F_CAL_LOG_DESC => '获取ICT产业版本号失败：未找到有效版本数据',
  --      F_RESULT_STATUS => X_RESULT_STATUS,
  --      F_ERRBUF => 'NO_DATA_FOUND');
  --     RETURN 'FAILED: 未找到有效版本数据';
  --   WHEN OTHERS THEN
  --     X_RESULT_STATUS := '0';
  --     PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  --     (F_SP_NAME => V_SP_NAME,
  --      F_STEP_NUM => V_STEP_NUM,
  --      F_CAL_LOG_DESC => '获取ICT产业版本号异常',
  --      F_RESULT_STATUS => X_RESULT_STATUS,
  --      F_ERRBUF => SQLSTATE || ':' || SQLERRM);
  --     RETURN 'FAILED: 获取版本号异常';
  -- END;

  -- ========== 12层卷积计算展开（消除循环，减少变量拼接）==========
  -- 卷积路径：ITEM -> CATEGORY -> MODL -> CEG -> DIMENSION -> SUBCATEGORY -> SUB_DETAIL -> SPART -> LV3 -> LV2 -> LV1 -> LV0

  -- 第1层：ITEM层级，跳过处理（作为卷积计算的起点）
  -- 业务含义：最细粒度的产品项目数据，无需处理
   DBMS_OUTPUT.PUT_LINE('第1层：ITEM层级，跳过处理（作为卷积计算的起点）');
  -- 第2层：CATEGORY -> MODL 卷积模块层级
  DBMS_OUTPUT.PUT_LINE('第2层：CATEGORY -> MODL 卷积模块层级');
  V_STEP_NUM := V_STEP_NUM + 1;
  WITH BASE_INDEX AS
   (SELECT PERIOD_YEAR, PERIOD_ID,
           PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
           DMS_CODE, DMS_CN_NAME,
           LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
           LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
           LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME,
           LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME,
           DIMENSION_CODE, DIMENSION_CN_NAME,
           DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
           DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,
           SPART_CODE, SPART_CN_NAME,
           TOP_L3_CEG_CODE, TOP_L3_CEG_SHORT_CN_NAME,
           TOP_L4_CEG_CODE AS PARENT_CODE,
           TOP_L4_CEG_SHORT_CN_NAME AS PARENT_NAME,
           GROUP_CODE, COST_INDEX, VIEW_FLAG, SCENARIO_FLAG, CALIBER_FLAG,
           OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, GROUP_LEVEL
      FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS
     WHERE VERSION_ID = 62111
       AND UPPER(GROUP_LEVEL) = 'CATEGORY'
       AND BASE_PERIOD_ID = 202401),

  LEV_WEIGHT AS
   (SELECT PROD_RND_TEAM_CODE,
           DIMENSION_CODE, DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUB_DETAIL_CODE,
           SPART_CODE, DMS_CODE, DMS_CN_NAME,
           GROUP_CODE, WEIGHT_RATE, PARENT_CODE, VIEW_FLAG, CALIBER_FLAG,
           OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, GROUP_LEVEL
      FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T
     WHERE VERSION_ID = 62111
       AND UPPER(GROUP_LEVEL) = 'CATEGORY' )

  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS
    (VERSION_ID, BASE_PERIOD_ID, PERIOD_YEAR, PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME,
     DIMENSION_CODE, DIMENSION_CN_NAME,
     DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
     DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,
     SPART_CODE, SPART_CN_NAME,
     TOP_L3_CEG_CODE, TOP_L3_CEG_SHORT_CN_NAME,
     VIEW_FLAG, PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
     DMS_CODE, DMS_CN_NAME, GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL,
     COST_INDEX, PARENT_CODE, PARENT_CN_NAME, SCENARIO_FLAG,
     CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE, DEL_FLAG,
     CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME)
  SELECT 62111 AS VERSION_ID,
         202401 AS BASE_PERIOD_ID,
         T1.PERIOD_YEAR, T1.PERIOD_ID,
         T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
         T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUB_DETAIL_CODE, T1.DIMENSION_SUB_DETAIL_CN_NAME,
         T1.SPART_CODE, T1.SPART_CN_NAME,
         T1.TOP_L3_CEG_CODE, T1.TOP_L3_CEG_SHORT_CN_NAME,
         T1.VIEW_FLAG, T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME,
         T1.DMS_CODE, T1.DMS_CN_NAME,
         T1.PARENT_CODE AS GROUP_CODE, T1.PARENT_NAME AS GROUP_CN_NAME,
         'MODL' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.TOP_L3_CEG_CODE AS PARENT_CODE, T1.TOP_L3_CEG_SHORT_CN_NAME AS PARENT_CN_NAME,
         T1.SCENARIO_FLAG, '-1' AS CREATED_BY, CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY, CURRENT_TIMESTAMP AS LAST_UPDATE_DATE, 'N' AS DEL_FLAG,
         T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
    FROM BASE_INDEX T1
    JOIN LEV_WEIGHT T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
     AND T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
     AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
     AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE
     AND NVL(T1.DMS_CODE,'DD') = NVL(T2.DMS_CODE,'DD')
     AND NVL(T1.DIMENSION_CODE,'D1') = NVL(T2.DIMENSION_CODE,'D1')
     AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'D2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'D2')
     AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,'D3') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,'D3')
     AND NVL(T1.SPART_CODE,'D4') = NVL(T2.SPART_CODE,'D4')
     AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
   GROUP BY T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
            T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME,
            T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME,
            T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
            T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME,
            T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME,
            T1.DIMENSION_SUB_DETAIL_CODE, T1.DIMENSION_SUB_DETAIL_CN_NAME,
            T1.SPART_CODE, T1.SPART_CN_NAME,
            T1.TOP_L3_CEG_CODE, T1.TOP_L3_CEG_SHORT_CN_NAME,
            T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME,
            T1.DMS_CODE, T1.DMS_CN_NAME,
            T1.SCENARIO_FLAG, T1.PERIOD_YEAR, T1.PERIOD_ID,
            T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG, T1.GROUP_LEVEL,
            T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;

  --EXECUTE IMMEDIATE V_SQL;

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第2层 MODL 层级指数收敛完成，影响行数：' || SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL, F_RESULT_STATUS => X_RESULT_STATUS,
   F_DML_ROW_COUNT => SQL%ROWCOUNT, F_ERRBUF => 'SUCCESS');

  -- 第3层：MODL -> CEG 卷积专家团层级
  DBMS_OUTPUT.PUT_LINE('第3层：MODL -> CEG 卷积专家团层级');
  V_STEP_NUM := V_STEP_NUM + 1;
  
  WITH BASE_INDEX AS
   (SELECT PERIOD_YEAR, PERIOD_ID,
           PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
           DMS_CODE, DMS_CN_NAME,
           LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
           LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
           LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME,
           LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME,
           DIMENSION_CODE, DIMENSION_CN_NAME,
           DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
           DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,
           SPART_CODE, SPART_CN_NAME,
           TOP_L3_CEG_CODE AS PARENT_CODE,
           TOP_L3_CEG_SHORT_CN_NAME AS PARENT_NAME,
           GROUP_CODE, COST_INDEX, VIEW_FLAG, SCENARIO_FLAG, CALIBER_FLAG,
           OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, GROUP_LEVEL
      FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS
     WHERE VERSION_ID = 62111
       AND UPPER(GROUP_LEVEL) = 'MODL'
       AND BASE_PERIOD_ID = 202401),

  LEV_WEIGHT AS
   (SELECT PROD_RND_TEAM_CODE,
           DIMENSION_CODE, DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUB_DETAIL_CODE,
           SPART_CODE, DMS_CODE, DMS_CN_NAME,
           GROUP_CODE, WEIGHT_RATE, PARENT_CODE, VIEW_FLAG, CALIBER_FLAG,
           OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, GROUP_LEVEL
      FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T
     WHERE VERSION_ID = 62111
       AND UPPER(GROUP_LEVEL) = 'MODL' )

  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS
    (VERSION_ID, BASE_PERIOD_ID, PERIOD_YEAR, PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME,
     DIMENSION_CODE, DIMENSION_CN_NAME,
     DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
     DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,
     SPART_CODE, SPART_CN_NAME,
     VIEW_FLAG, PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
     DMS_CODE, DMS_CN_NAME, GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL,
     COST_INDEX, PARENT_CODE, PARENT_CN_NAME, SCENARIO_FLAG,
     CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE, DEL_FLAG,
     CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME)
  SELECT 62111 AS VERSION_ID,
         202401 AS BASE_PERIOD_ID,
         T1.PERIOD_YEAR, T1.PERIOD_ID,
         T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
         T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUB_DETAIL_CODE, T1.DIMENSION_SUB_DETAIL_CN_NAME,
         T1.SPART_CODE, T1.SPART_CN_NAME,
         T1.VIEW_FLAG, T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME,
         T1.DMS_CODE, T1.DMS_CN_NAME,
         T1.PARENT_CODE AS GROUP_CODE, T1.PARENT_NAME AS GROUP_CN_NAME,
         'CEG' AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         T1.DMS_CODE AS PARENT_CODE, T1.DMS_CN_NAME AS PARENT_CN_NAME,
         T1.SCENARIO_FLAG, '-1' AS CREATED_BY, CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY, CURRENT_TIMESTAMP AS LAST_UPDATE_DATE, 'N' AS DEL_FLAG,
         T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
    FROM BASE_INDEX T1
    JOIN LEV_WEIGHT T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
     AND T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
     AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
     AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE
     AND NVL(T1.DMS_CODE,'DD') = NVL(T2.DMS_CODE,'DD')
     AND NVL(T1.DIMENSION_CODE,'D1') = NVL(T2.DIMENSION_CODE,'D1')
     AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'D2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'D2')
     AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,'D3') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,'D3')
     AND NVL(T1.SPART_CODE,'D4') = NVL(T2.SPART_CODE,'D4')
     AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
   GROUP BY T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
            T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME,
            T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME,
            T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
            T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME,
            T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME,
            T1.DIMENSION_SUB_DETAIL_CODE, T1.DIMENSION_SUB_DETAIL_CN_NAME,
            T1.SPART_CODE, T1.SPART_CN_NAME,
            T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME,
            T1.DMS_CODE, T1.DMS_CN_NAME,
            T1.SCENARIO_FLAG, T1.PERIOD_YEAR, T1.PERIOD_ID,
            T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG, T1.GROUP_LEVEL,
            T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第3层 CEG 层级指数收敛完成，影响行数：' || SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL, F_RESULT_STATUS => X_RESULT_STATUS,
   F_DML_ROW_COUNT => SQL%ROWCOUNT, F_ERRBUF => 'SUCCESS');

  -- 第4层：CEG -> DIMENSION/SUBCATEGORY/SUB_DETAIL/SPART 量纲颗粒度层级分视角处理
  V_STEP_NUM := V_STEP_NUM + 1;
  WITH BASE_INDEX AS
   (SELECT PERIOD_YEAR, PERIOD_ID,
           PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
           DMS_CODE, DMS_CN_NAME,
           LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
           LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
           LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME,
           LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME,
           DIMENSION_CODE, DIMENSION_CN_NAME,
           DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
           DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,
           SPART_CODE, SPART_CN_NAME,
           DMS_CODE AS PARENT_CODE,
           DMS_CN_NAME AS PARENT_NAME,
           GROUP_CODE, COST_INDEX, VIEW_FLAG, SCENARIO_FLAG, CALIBER_FLAG,
           OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, GROUP_LEVEL
      FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS
     WHERE VERSION_ID = 62111
       AND UPPER(GROUP_LEVEL) = 'CEG'
       AND BASE_PERIOD_ID = 202401),

  LEV_WEIGHT AS
   (SELECT PROD_RND_TEAM_CODE,
           DIMENSION_CODE, DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUB_DETAIL_CODE,
           SPART_CODE, DMS_CODE, DMS_CN_NAME,
           GROUP_CODE, WEIGHT_RATE, PARENT_CODE, VIEW_FLAG, CALIBER_FLAG,
           OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, GROUP_LEVEL
      FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T
     WHERE VERSION_ID = 62111
       AND UPPER(GROUP_LEVEL) = 'CEG' )

  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS
    (VERSION_ID, BASE_PERIOD_ID, PERIOD_YEAR, PERIOD_ID,
     LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
     LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME,
     DIMENSION_CODE, DIMENSION_CN_NAME,
     DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
     DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,
     SPART_CODE, SPART_CN_NAME,
     VIEW_FLAG, PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
     DMS_CODE, DMS_CN_NAME, GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL,
     COST_INDEX, PARENT_CODE, PARENT_CN_NAME, SCENARIO_FLAG,
     CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE, DEL_FLAG,
     CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME)
  SELECT 62111 AS VERSION_ID,
         202401 AS BASE_PERIOD_ID,
         T1.PERIOD_YEAR, T1.PERIOD_ID,
         T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
         T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUB_DETAIL_CODE, T1.DIMENSION_SUB_DETAIL_CN_NAME,
         T1.SPART_CODE, T1.SPART_CN_NAME,
         T1.VIEW_FLAG, T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME,
         T1.DMS_CODE, T1.DMS_CN_NAME,
         T1.PARENT_CODE AS GROUP_CODE, T1.PARENT_NAME AS GROUP_CN_NAME,
         CASE WHEN T1.VIEW_FLAG IN ('0','3','6') THEN 'DIMENSION'
              WHEN T1.VIEW_FLAG IN ('1','4','7') THEN 'SUBCATEGORY'
              WHEN T1.VIEW_FLAG IN ('2','5','8') THEN 'SUB_DETAIL'
              ELSE 'SPART'
         END AS GROUP_LEVEL,
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE) AS COST_INDEX,
         CASE WHEN T1.VIEW_FLAG IN ('0','3','6') THEN T1.PROD_RND_TEAM_CODE
              WHEN T1.VIEW_FLAG IN ('1','4','7') THEN T1.DIMENSION_CODE
              WHEN T1.VIEW_FLAG IN ('2','5','8') THEN T1.DIMENSION_SUBCATEGORY_CODE
              ELSE T1.DIMENSION_SUB_DETAIL_CODE
         END AS PARENT_CODE,
         CASE WHEN T1.VIEW_FLAG IN ('0','3','6') THEN T1.PROD_RND_TEAM_CN_NAME
              WHEN T1.VIEW_FLAG IN ('1','4','7') THEN T1.DIMENSION_CN_NAME
              WHEN T1.VIEW_FLAG IN ('2','5','8') THEN T1.DIMENSION_SUBCATEGORY_CN_NAME
              ELSE T1.DIMENSION_SUB_DETAIL_CN_NAME
         END AS PARENT_CN_NAME,
         T1.SCENARIO_FLAG, '-1' AS CREATED_BY, CURRENT_TIMESTAMP AS CREATION_DATE,
         '-1' AS LAST_UPDATED_BY, CURRENT_TIMESTAMP AS LAST_UPDATE_DATE, 'N' AS DEL_FLAG,
         T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
    FROM BASE_INDEX T1
    JOIN LEV_WEIGHT T2
      ON T1.GROUP_CODE = T2.GROUP_CODE
     AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL
     AND T1.PARENT_CODE = T2.PARENT_CODE
     AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
     AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE
     AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE
     AND NVL(T1.DMS_CODE,'DD') = NVL(T2.DMS_CODE,'DD')
     AND NVL(T1.DIMENSION_CODE,'D1') = NVL(T2.DIMENSION_CODE,'D1')
     AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'D2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'D2')
     AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,'D3') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,'D3')
     AND NVL(T1.SPART_CODE,'D4') = NVL(T2.SPART_CODE,'D4')
     AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
   GROUP BY T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
            T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME,
            T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME,
            T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
            T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME,
            T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME,
            T1.DIMENSION_SUB_DETAIL_CODE, T1.DIMENSION_SUB_DETAIL_CN_NAME,
            T1.SPART_CODE, T1.SPART_CN_NAME,
            T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME,
            T1.DMS_CODE, T1.DMS_CN_NAME,
            T1.SCENARIO_FLAG, T1.PERIOD_YEAR, T1.PERIOD_ID,
            T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG, T1.GROUP_LEVEL,
            T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第4层 量纲颗粒度分视角 层级指数收敛完成，影响行数：' || SQL%ROWCOUNT,
   F_FORMULA_SQL_TXT => V_SQL, F_RESULT_STATUS => X_RESULT_STATUS,
   F_DML_ROW_COUNT => SQL%ROWCOUNT, F_ERRBUF => 'SUCCESS');

  -- 第5层：SPART -> SUB_DETAIL 量纲子类明细卷积
  V_STEP_NUM := V_STEP_NUM + 1;
  WITH BASE_INDEX AS
   (SELECT PERIOD_YEAR, PERIOD_ID, PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
           DMS_CODE, DMS_CN_NAME, LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
           LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME,
           LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME,
           LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME,
           DIMENSION_CODE, DIMENSION_CN_NAME, DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
           DIMENSION_SUB_DETAIL_CODE AS PARENT_CODE, DIMENSION_SUB_DETAIL_CN_NAME AS PARENT_NAME,
           GROUP_CODE, COST_INDEX, VIEW_FLAG, SCENARIO_FLAG, CALIBER_FLAG,
           OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, GROUP_LEVEL
      FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS WHERE VERSION_ID = 62111
       AND UPPER(GROUP_LEVEL) = 'SPART' AND BASE_PERIOD_ID = 202401),
  LEV_WEIGHT AS
   (SELECT PROD_RND_TEAM_CODE, DIMENSION_CODE, DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUB_DETAIL_CODE,
           DMS_CODE, DMS_CN_NAME, GROUP_CODE, WEIGHT_RATE, PARENT_CODE, VIEW_FLAG, CALIBER_FLAG,
           OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME, GROUP_LEVEL
      FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'SPART' )
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS
    (VERSION_ID, BASE_PERIOD_ID, PERIOD_YEAR, PERIOD_ID, LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
     LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME, LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME,
     LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME, DIMENSION_CODE, DIMENSION_CN_NAME,
     DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME, VIEW_FLAG, PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
     DMS_CODE, DMS_CN_NAME, GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL, COST_INDEX, PARENT_CODE, PARENT_CN_NAME,
     SCENARIO_FLAG, CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE, DEL_FLAG,
     CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME)
  SELECT 62111, 202401, T1.PERIOD_YEAR, T1.PERIOD_ID,
         T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME, T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME, T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
         T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME, T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.VIEW_FLAG, T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME, T1.DIMENSION_SUB_DETAIL_CODE, T1.DIMENSION_SUB_DETAIL_CN_NAME,
         T1.PARENT_CODE, T1.PARENT_NAME, 'SUB_DETAIL', SUM(T1.COST_INDEX * T2.WEIGHT_RATE),
         T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME, T1.SCENARIO_FLAG, '-1', CURRENT_TIMESTAMP, '-1', CURRENT_TIMESTAMP, 'N',
         T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
    FROM BASE_INDEX T1 JOIN LEV_WEIGHT T2 ON T1.GROUP_CODE = T2.GROUP_CODE AND T1.VIEW_FLAG = T2.VIEW_FLAG
     AND T1.GROUP_LEVEL = T2.GROUP_LEVEL AND T1.PARENT_CODE = T2.PARENT_CODE AND T1.OVERSEA_FLAG = T2.OVERSEA_FLAG
     AND T1.LV0_PROD_LIST_CODE = T2.LV0_PROD_LIST_CODE AND T1.PROD_RND_TEAM_CODE = T2.PROD_RND_TEAM_CODE
     AND NVL(T1.DMS_CODE,'DD') = NVL(T2.DMS_CODE,'DD') AND NVL(T1.DIMENSION_CODE,'D1') = NVL(T2.DIMENSION_CODE,'D1')
     AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'D2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'D2')
     AND NVL(T1.DIMENSION_SUB_DETAIL_CODE,'D3') = NVL(T2.DIMENSION_SUB_DETAIL_CODE,'D3') AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
   GROUP BY T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME, T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME,
            T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME, T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
            T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME, T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME,
            T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME, T1.DIMENSION_SUB_DETAIL_CODE, T1.DIMENSION_SUB_DETAIL_CN_NAME,
            T1.SCENARIO_FLAG, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG, T1.GROUP_LEVEL,
            T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;

  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
  (F_SP_NAME => V_SP_NAME, F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第5层 SUB_DETAIL 层级指数收敛完成，影响行数：' || SQL%ROWCOUNT,
   F_RESULT_STATUS => X_RESULT_STATUS, F_DML_ROW_COUNT => SQL%ROWCOUNT, F_ERRBUF => 'SUCCESS');

  -- 第6层：SUB_DETAIL -> SUBCATEGORY 量纲子类卷积
  V_STEP_NUM := V_STEP_NUM + 1;
  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS (VERSION_ID, BASE_PERIOD_ID, PERIOD_YEAR, PERIOD_ID, LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME, LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME, LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME,
   DIMENSION_CODE, DIMENSION_CN_NAME, VIEW_FLAG, PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME, DMS_CODE, DMS_CN_NAME, GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL,
   COST_INDEX, PARENT_CODE, PARENT_CN_NAME, SCENARIO_FLAG, CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE, DEL_FLAG,
   CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME)
  SELECT 62111, 202401, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
   T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME, T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME, T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
   T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME, T1.VIEW_FLAG, T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME, T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME,
   T1.PARENT_CODE, T1.PARENT_NAME, 'SUBCATEGORY', SUM(T1.COST_INDEX * T2.WEIGHT_RATE), T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME, T1.SCENARIO_FLAG,
   '-1', CURRENT_TIMESTAMP, '-1', CURRENT_TIMESTAMP, 'N', T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
  FROM (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'SUB_DETAIL') T1
  JOIN (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'SUB_DETAIL') T2
   ON T1.GROUP_CODE = T2.GROUP_CODE AND T1.VIEW_FLAG = T2.VIEW_FLAG AND T1.PARENT_CODE = T2.PARENT_CODE AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
  GROUP BY T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME, T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME,
   T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME, T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME, T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME,
   T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME, T1.DIMENSION_SUBCATEGORY_CODE, T1.DIMENSION_SUBCATEGORY_CN_NAME, T1.SCENARIO_FLAG, T1.PERIOD_YEAR, T1.PERIOD_ID,
   T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG, T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;
   
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(F_SP_NAME => V_SP_NAME, F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第6层 SUBCATEGORY 层级指数收敛完成，影响行数：' || SQL%ROWCOUNT, F_RESULT_STATUS => X_RESULT_STATUS, F_DML_ROW_COUNT => SQL%ROWCOUNT, F_ERRBUF => 'SUCCESS');

  -- 第7层：SUBCATEGORY -> DIMENSION 量纲卷积
  V_STEP_NUM := V_STEP_NUM + 1;

  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS (VERSION_ID, BASE_PERIOD_ID, PERIOD_YEAR, PERIOD_ID, LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME, LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME, LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME,
   VIEW_FLAG, PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME, DMS_CODE, DMS_CN_NAME, GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL, COST_INDEX, PARENT_CODE, PARENT_CN_NAME,
   SCENARIO_FLAG, CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE, DEL_FLAG, CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME)
  SELECT 62111, 202401, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
   T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME, T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME, T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
   T1.VIEW_FLAG, T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME, T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME, T1.PARENT_CODE, T1.PARENT_NAME, 'DIMENSION',
   SUM(T1.COST_INDEX * T2.WEIGHT_RATE), T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME, T1.SCENARIO_FLAG, '-1', CURRENT_TIMESTAMP, '-1', CURRENT_TIMESTAMP, 'N',
   T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
  FROM (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'SUBCATEGORY') T1
  JOIN (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'SUBCATEGORY') T2
   ON T1.GROUP_CODE = T2.GROUP_CODE AND T1.VIEW_FLAG = T2.VIEW_FLAG AND T1.PARENT_CODE = T2.PARENT_CODE AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
  GROUP BY T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME, T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME, T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME,
   T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME, T1.PROD_RND_TEAM_CODE, T1.PROD_RND_TEAM_CN_NAME, T1.DIMENSION_CODE, T1.DIMENSION_CN_NAME, T1.SCENARIO_FLAG,
   T1.PERIOD_YEAR, T1.PERIOD_ID, T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG, T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(F_SP_NAME => V_SP_NAME, F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第7层 DIMENSION 层级指数收敛完成，影响行数：' || SQL%ROWCOUNT, F_RESULT_STATUS => X_RESULT_STATUS, F_DML_ROW_COUNT => SQL%ROWCOUNT, F_ERRBUF => 'SUCCESS');

  -- 第8层：DIMENSION -> LV1/LV2/LV3 分视角卷积量纲父级-重量级团队
  V_STEP_NUM := V_STEP_NUM + 1;

  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS (VERSION_ID, BASE_PERIOD_ID, PERIOD_YEAR, PERIOD_ID, LV0_PROD_RND_TEAM_CODE, LV0_PROD_RD_TEAM_CN_NAME,
   LV1_PROD_RND_TEAM_CODE, LV1_PROD_RD_TEAM_CN_NAME, LV2_PROD_RND_TEAM_CODE, LV2_PROD_RD_TEAM_CN_NAME, LV3_PROD_RND_TEAM_CODE, LV3_PROD_RD_TEAM_CN_NAME,
   VIEW_FLAG, PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME, GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL, COST_INDEX, PARENT_CODE, PARENT_CN_NAME,
   SCENARIO_FLAG, CREATED_BY, CREATION_DATE, LAST_UPDATED_BY, LAST_UPDATE_DATE, DEL_FLAG, CALIBER_FLAG, OVERSEA_FLAG, LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME)
  SELECT 62111, 202401, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
   T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME, T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME, T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME,
   T1.VIEW_FLAG, T1.PARENT_CODE, T1.PARENT_CN_NAME, T1.PARENT_CODE, T1.PARENT_CN_NAME,
   CASE WHEN T1.VIEW_FLAG IN ('0','1','2','9') THEN 'LV1' WHEN T1.VIEW_FLAG IN ('3','4','5','10') THEN 'LV2' ELSE 'LV3' END,
   SUM(T1.COST_INDEX * T2.WEIGHT_RATE),
   CASE WHEN T1.VIEW_FLAG IN ('0','1','2','9') THEN T1.LV0_PROD_RND_TEAM_CODE WHEN T1.VIEW_FLAG IN ('3','4','5','10') THEN T1.LV1_PROD_RND_TEAM_CODE ELSE T1.LV2_PROD_RND_TEAM_CODE END,
   CASE WHEN T1.VIEW_FLAG IN ('0','1','2','9') THEN T1.LV0_PROD_RD_TEAM_CN_NAME WHEN T1.VIEW_FLAG IN ('3','4','5','10') THEN T1.LV1_PROD_RD_TEAM_CN_NAME ELSE T1.LV2_PROD_RD_TEAM_CN_NAME END,
   T1.SCENARIO_FLAG, '-1', CURRENT_TIMESTAMP, '-1', CURRENT_TIMESTAMP, 'N', T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
  FROM (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'DIMENSION') T1
  JOIN (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'DIMENSION') T2
   ON T1.GROUP_CODE = T2.GROUP_CODE AND T1.VIEW_FLAG = T2.VIEW_FLAG AND T1.PARENT_CODE = T2.PARENT_CODE AND T1.CALIBER_FLAG = T2.CALIBER_FLAG
  GROUP BY T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME, T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME, T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME,
   T1.LV3_PROD_RND_TEAM_CODE, T1.LV3_PROD_RD_TEAM_CN_NAME, T1.SCENARIO_FLAG, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG,
   T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;
  PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(F_SP_NAME => V_SP_NAME, F_STEP_NUM => V_STEP_NUM,
   F_CAL_LOG_DESC => '第8层 分视角重量级团队 层级指数收敛完成，影响行数：' || SQL%ROWCOUNT, F_RESULT_STATUS => X_RESULT_STATUS, F_DML_ROW_COUNT => SQL%ROWCOUNT, F_ERRBUF => 'SUCCESS');

  -- 第9-12层：LV3->LV2->LV1->LV0 重量级团队层级卷积
  FOR i IN 9..12 LOOP
    V_STEP_NUM := V_STEP_NUM + 1;
    CASE i
      WHEN 9 THEN -- LV3层级
        INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS 
        SELECT 62111, 202401, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME, T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME, T1.VIEW_FLAG, T1.PARENT_CODE, T1.PARENT_NAME,
         T1.PARENT_CODE, T1.PARENT_NAME, 'LV3', SUM(T1.COST_INDEX * T2.WEIGHT_RATE), T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME, T1.SCENARIO_FLAG,
         '-1', CURRENT_TIMESTAMP, '-1', CURRENT_TIMESTAMP, 'N', T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
         FROM (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'LV4') T1
         JOIN (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'LV4') T2 ON T1.GROUP_CODE = T2.GROUP_CODE
         GROUP BY T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME, T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME, T1.LV2_PROD_RND_TEAM_CODE, T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.SCENARIO_FLAG, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG, T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;
      WHEN 10 THEN -- LV2层级
        INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS 
        SELECT 62111, 202401, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME, T1.VIEW_FLAG, T1.PARENT_CODE, T1.PARENT_NAME, T1.PARENT_CODE, T1.PARENT_NAME, 'LV2',
         SUM(T1.COST_INDEX * T2.WEIGHT_RATE), T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME, T1.SCENARIO_FLAG, '-1', CURRENT_TIMESTAMP, '-1', CURRENT_TIMESTAMP, 'N',
         T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
         FROM (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'LV3') T1
         JOIN (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'LV3') T2 ON T1.GROUP_CODE = T2.GROUP_CODE
         GROUP BY T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME, T1.LV1_PROD_RND_TEAM_CODE, T1.LV1_PROD_RD_TEAM_CN_NAME, T1.SCENARIO_FLAG, T1.PERIOD_YEAR, T1.PERIOD_ID,
         T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG, T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;
      WHEN 11 THEN -- LV1层级
        INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS SELECT 62111, 202401, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.VIEW_FLAG, T1.PARENT_CODE, T1.PARENT_NAME, T1.PARENT_CODE, T1.PARENT_NAME, 'LV1', SUM(T1.COST_INDEX * T2.WEIGHT_RATE), T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME,
         T1.SCENARIO_FLAG, '-1', CURRENT_TIMESTAMP, '-1', CURRENT_TIMESTAMP, 'N', T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
         FROM (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'LV2') T1
         JOIN (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'LV2') T2 ON T1.GROUP_CODE = T2.GROUP_CODE
         GROUP BY T1.LV0_PROD_RND_TEAM_CODE, T1.LV0_PROD_RD_TEAM_CN_NAME, T1.SCENARIO_FLAG, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG,
         T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;
      WHEN 12 THEN -- LV0层级
        INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS SELECT 62111, 202401, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.VIEW_FLAG, T1.PARENT_CODE, T1.PARENT_NAME,
         T1.PARENT_CODE, T1.PARENT_NAME, 'LV0', SUM(T1.COST_INDEX * T2.WEIGHT_RATE), '', '', T1.SCENARIO_FLAG, '-1', CURRENT_TIMESTAMP, '-1', CURRENT_TIMESTAMP, 'N',
         T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME
         FROM (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'LV1') T1
         JOIN (SELECT * FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_WEIGHT_T WHERE VERSION_ID = 62111 AND UPPER(GROUP_LEVEL) = 'LV1') T2 ON T1.GROUP_CODE = T2.GROUP_CODE
         GROUP BY T1.SCENARIO_FLAG, T1.PERIOD_YEAR, T1.PERIOD_ID, T1.PARENT_NAME, T1.PARENT_CODE, T1.VIEW_FLAG, T1.CALIBER_FLAG, T1.OVERSEA_FLAG, T1.LV0_PROD_LIST_CODE, T1.LV0_PROD_LIST_CN_NAME;
    END CASE;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T(F_SP_NAME => V_SP_NAME, F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => '第' || i || '层 层级指数收敛完成，影响行数：' || SQL%ROWCOUNT, F_RESULT_STATUS => X_RESULT_STATUS, F_DML_ROW_COUNT => SQL%ROWCOUNT, F_ERRBUF => 'SUCCESS');
  END LOOP;

  -- 删除目标表中同版本的旧数据
  V_STEP_NUM := V_STEP_NUM + 1;
  V_SQL := 'DELETE FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_T WHERE VERSION_ID = 62111;';

  BEGIN
    EXECUTE IMMEDIATE V_SQL;

    -- 记录删除操作日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => '指数表同版本数据删除完成，删除行数：' || SQL%ROWCOUNT,
     F_FORMULA_SQL_TXT => V_SQL,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_ERRBUF => 'SUCCESS');

  EXCEPTION
    WHEN OTHERS THEN
      X_RESULT_STATUS := '0';
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
      (F_SP_NAME => V_SP_NAME,
       F_STEP_NUM => V_STEP_NUM,
       F_CAL_LOG_DESC => '删除指数表同版本数据失败',
       F_FORMULA_SQL_TXT => V_SQL,
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_ERRBUF => SQLSTATE || ':' || SQLERRM);
      RETURN 'FAILED: 删除旧数据异常';
  END;

  -- 将中间表数据插入到目标表（固化字段名，减少变量拼接）
  V_STEP_NUM := V_STEP_NUM + 1;
  
  INSERT INTO FIN_DM_OPT_FOI.DM_FOC_DMS_MONTH_COST_IDX_T
    (VERSION_ID, PERIOD_YEAR, PERIOD_ID, BASE_PERIOD_ID,
     PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
     DIMENSION_CODE, DIMENSION_CN_NAME,
     DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
     DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,
     SPART_CODE, SPART_CN_NAME, DMS_CODE, DMS_CN_NAME,
     GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL, COST_INDEX,
     PARENT_CODE, PARENT_CN_NAME, CREATED_BY, CREATION_DATE,
     LAST_UPDATED_BY, LAST_UPDATE_DATE, DEL_FLAG, VIEW_FLAG,
     APPEND_FLAG, SCENARIO_FLAG, CALIBER_FLAG, OVERSEA_FLAG,
     LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME)
  SELECT 62111 AS VERSION_ID,
         PERIOD_YEAR, PERIOD_ID, BASE_PERIOD_ID,
         PROD_RND_TEAM_CODE, PROD_RND_TEAM_CN_NAME,
         DIMENSION_CODE, DIMENSION_CN_NAME,
         DIMENSION_SUBCATEGORY_CODE, DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUB_DETAIL_CODE, DIMENSION_SUB_DETAIL_CN_NAME,
         SPART_CODE, SPART_CN_NAME, DMS_CODE, DMS_CN_NAME,
         GROUP_CODE, GROUP_CN_NAME, GROUP_LEVEL, COST_INDEX,
         PARENT_CODE, PARENT_CN_NAME, CREATED_BY, CREATION_DATE,
         LAST_UPDATED_BY, LAST_UPDATE_DATE, DEL_FLAG, VIEW_FLAG,
         APPEND_FLAG, SCENARIO_FLAG, CALIBER_FLAG, OVERSEA_FLAG,
         LV0_PROD_LIST_CODE, LV0_PROD_LIST_CN_NAME
    FROM FIN_DM_OPT_FOI.DM_FOC_DMS_MID_MONTH_IDX_T_DMS;

    -- 记录插入操作成功日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => '指数表插数完成，插入行数：' || SQL%ROWCOUNT,
     F_FORMULA_SQL_TXT => V_SQL,
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_DML_ROW_COUNT => SQL%ROWCOUNT,
     F_ERRBUF => 'SUCCESS');

    -- 记录函数执行完成日志
    V_STEP_NUM := V_STEP_NUM + 1;
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => V_SP_NAME || '执行完成 - ICT产业量纲颗粒度月度指数计算成功',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => 'SUCCESS');

  EXCEPTION
    WHEN OTHERS THEN
      X_RESULT_STATUS := '0';
      PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
      (F_SP_NAME => V_SP_NAME,
       F_STEP_NUM => V_STEP_NUM,
       F_CAL_LOG_DESC => '指数表插数失败',
       F_FORMULA_SQL_TXT => V_SQL,
       F_RESULT_STATUS => X_RESULT_STATUS,
       F_ERRBUF => SQLSTATE || ':' || SQLERRM);
      RETURN 'FAILED: 插入数据异常';
  END;

  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
    X_RESULT_STATUS := '0';

    -- 记录全局异常日志
    PERFORM FIN_DM_OPT_FOI.F_DM_FOI_CAL_LOG_T
    (F_SP_NAME => V_SP_NAME,
     F_STEP_NUM => V_STEP_NUM,
     F_CAL_LOG_DESC => V_SP_NAME || '第' || V_STEP_NUM || '步运行失败',
     F_RESULT_STATUS => X_RESULT_STATUS,
     F_ERRBUF => SQLSTATE || ':' || SQLERRM);

    RETURN 'FAILED: ' || SQLSTATE || ':' || SQLERRM;

END;

$$
/
